name: Docker Image CI

on:
    workflow_dispatch:
        inputs:
            registry:
                description: 'Container Registry to push the image to.'
                type: choice
                required: true
                default: 'aws_ecr'
                options:
                    - 'docker_hub'
                    - 'aws_ecr'
            environment:
                description: 'Environment to push the image to.'
                required: true
                default: 'dev'
                type: choice
                options:
                    - dev
                    - prod
            image_type:
                description: 'Type of image to build and push.'
                type: choice
                required: true
                default: 'main'
                options:
                    - 'main'
                    - 'worker'
            node_version:
                description: 'Node.js version to build this image with.'
                type: choice
                required: true
                default: '20'
                options:
                    - '20'
            tag_version:
                description: 'Tag version of the image to be pushed.'
                type: string
                required: true
                default: 'latest'

jobs:
    docker:
        runs-on: ubuntu-latest
        environment: ${{ github.event.inputs.environment }}
        steps:
            - name: Set default values
              id: defaults
              run: |
                  echo "registry=${{ github.event.inputs.registry || 'aws_ecr' }}" >> $GITHUB_OUTPUT
                  echo "image_type=${{ github.event.inputs.image_type || 'main' }}" >> $GITHUB_OUTPUT
                  echo "node_version=${{ github.event.inputs.node_version || '20' }}" >> $GITHUB_OUTPUT
                  echo "tag_version=${{ github.event.inputs.tag_version || 'latest' }}" >> $GITHUB_OUTPUT

            - name: Checkout
              uses: actions/checkout@v4.1.1

            - name: Set up QEMU
              uses: docker/setup-qemu-action@v3.0.0

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3.0.0

            # ------------------------
            # Login Steps (conditional)
            # ------------------------
            - name: Login to Docker Hub
              if: steps.defaults.outputs.registry == 'docker_hub'
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKERHUB_USERNAME }}
                  password: ${{ secrets.DOCKERHUB_TOKEN }}

            - name: Configure AWS Credentials
              if: steps.defaults.outputs.registry == 'aws_ecr'
              uses: aws-actions/configure-aws-credentials@v3
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ${{ secrets.AWS_REGION }}

            - name: Login to Amazon ECR
              if: steps.defaults.outputs.registry == 'aws_ecr'
              uses: aws-actions/amazon-ecr-login@v1

            # -------------------------
            # Build and push (conditional tags)
            # -------------------------
            - name: Build and push
              uses: docker/build-push-action@v5.3.0
              with:
                  context: .
                  file: |
                      ${{ 
                        steps.defaults.outputs.image_type == 'worker' && 'docker/worker/Dockerfile' ||
                        (steps.defaults.outputs.registry == 'docker_hub' && './docker/Dockerfile' || 'Dockerfile') 
                      }}
                  build-args: |
                      NODE_VERSION=${{ steps.defaults.outputs.node_version }}
                  platforms: linux/amd64,linux/arm64
                  push: true
                  tags: |
                      ${{ 
                          steps.defaults.outputs.registry == 'docker_hub' && 
                          format('flowiseai/flowise{0}:{1}', 
                              steps.defaults.outputs.image_type == 'worker' && '-worker' || '', 
                              steps.defaults.outputs.tag_version) ||
                          format('{0}.dkr.ecr.{1}.amazonaws.com/flowise{2}:{3}', 
                              secrets.AWS_ACCOUNT_ID, 
                              secrets.AWS_REGION, 
                              steps.defaults.outputs.image_type == 'worker' && '-worker' || '',
                              steps.defaults.outputs.tag_version) 
                      }}
